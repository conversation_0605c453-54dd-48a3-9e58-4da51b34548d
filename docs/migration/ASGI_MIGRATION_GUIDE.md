# Odoo WSGI to ASGI Migration Guide

This document describes the migration from WSGI (Web Server Gateway Interface) to ASGI (Asynchronous Server Gateway Interface) in Odoo, along with the replacement of psycopg2 with asyncpg for async PostgreSQL operations.

## Overview

The migration involves several key changes:

1. **Server Architecture**: Replaced WSGI servers with ASGI servers (Uvicorn/Hypercorn)
2. **Database Layer**: Replaced psycopg2 with asyncpg for async PostgreSQL operations
3. **HTTP Layer**: Converted synchronous request/response handling to async patterns
4. **ORM Layer**: Updated Environment, Registry, and Cursor classes for async operation
5. **Controllers**: Added support for async route handlers and controllers

## Key Benefits

- **Better Performance**: Async I/O allows handling more concurrent requests
- **Improved Scalability**: Non-blocking database operations
- **Modern Architecture**: ASGI is the modern standard for Python web applications
- **WebSocket Support**: Native support for real-time features
- **Resource Efficiency**: Better memory and CPU utilization

## Migration Components

### 1. Database Layer (`odoo/sql_db.py`)

**New Features:**
- `ConnectionPool`: Connection pooling using asyncpg
- `Cursor`: Database cursor with async/await methods
- `Connection`: Wrapper for database connections

**Usage:**
```python
# Old (psycopg2)
import odoo.sql_db
db = odoo.sql_db.db_connect('mydb')
with db.cursor() as cr:
    cr.execute("SELECT * FROM users")
    result = cr.fetchall()

# New (asyncpg)
from odoo.sql_db import db_connect
connection = await db_connect('mydb')
async with connection.cursor() as cr:
    await cr.execute("SELECT * FROM users")
    result = await cr.fetchall()
```

### 2. ASGI Application (`odoo/http/asgi_application.py`)

**New Features:**
- `Application`: ASGI application class
- `OdooASGIMiddleware`: Middleware for request processing
- Native ASGI interface with `async def __call__(scope, receive, send)`

### 3. Async API Layer (`odoo/api.py`)

**New Features:**
- `Environment`: Async version of ORM Environment
- `Registry`: Async model registry
- `Transaction`: Async transaction management

**Usage:**
```python
# Old (sync)
env = api.Environment(cr, uid, context)
records = env['res.users'].search([])

# New (async)
from odoo.api import environment
async with environment(cr, uid, context) as env:
    records = await env['res.users'].search([])
```

### 4. Async Controllers (`odoo/http/controllers.py`)

**New Features:**
- `Controller`: Base class for async controllers
- `@route`: Decorator for async route handlers
- `Dispatcher`: Async request dispatcher

**Usage:**
```python
from odoo.http.controllers import Controller, route

class MyController(Controller):

    @route('/api/data', type='json', auth='user')
    async def get_data(self):
        # Async database operations
        data = await self.get_data_from_db()
        return {'data': data}

    async def get_data_from_db(self):
        # Async database query
        async with self.env.cr.cursor() as cr:
            await cr.execute("SELECT * FROM my_table")
            return await cr.fetchall()
```

### 5. ASGI Server (`odoo/service/asgi_server.py`)

**New Features:**
- `Server`: Uvicorn-based ASGI server with modern WebSocket support
- `GeventServer`: ASGI server with gevent compatibility
- `PreforkServer`: Multi-process ASGI server
- Modern WebSocket configuration using `wsproto` instead of legacy `websockets`

### 6. WebSocket Support (`odoo/http/websocket*.py`)

**New Features:**
- `WebSocketConnection`: Modern WebSocket connection management
- `WebSocketController`: Controller pattern for WebSocket endpoints
- `WebSocketBusService`: Real-time messaging and notifications
- Authentication integration with Odoo's session system
- Channel-based communication and subscriptions
- Integration with existing `bus.bus` model

## Configuration Changes

### 1. Enable ASGI Mode

Add to your `odoo.conf`:
```ini
[options]
asgi_enable = True
```

### 2. Server Configuration

**Development:**
```bash
# Using the new ASGI server
python odoo-bin async --dev=reload,qweb,werkzeug,xml

# Using uvicorn directly
uvicorn --host 127.0.0.1 --port 8069 --reload setup.odoo-asgi:application
```

**Production:**
```bash
# Multi-worker ASGI server
uvicorn --host 0.0.0.0 --port 8069 --workers 4 setup.odoo-asgi:application

# With Gunicorn
gunicorn -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8069 setup.odoo-asgi:application
```

### 3. Database Configuration

The database configuration remains the same, but now uses async connections:
```ini
[options]
db_host = localhost
db_port = 5432
db_user = odoo
db_password = odoo
db_maxconn = 64
```

## Deployment Guide

### 1. Docker Deployment

```dockerfile
FROM python:3.12-slim

WORKDIR /opt/odoo

# Install dependencies
COPY requirements.txt .
RUN pip install -r requirements.txt

# Copy Odoo code
COPY . .

# Expose port
EXPOSE 8069

# Run ASGI server
CMD ["uvicorn", "--host", "0.0.0.0", "--port", "8069", "--workers", "4", "setup.odoo-asgi:application"]
```

### 2. Systemd Service

```ini
[Unit]
Description=Odoo ASGI Server
After=network.target postgresql.service

[Service]
Type=exec
User=odoo
Group=odoo
WorkingDirectory=/opt/odoo
Environment=PATH=/opt/odoo/venv/bin
ExecStart=/opt/odoo/venv/bin/uvicorn --host 0.0.0.0 --port 8069 --workers 4 setup.odoo-asgi:application
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
```

### 3. Nginx Reverse Proxy

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:8069;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
```

## Testing the Migration

### 1. Run Migration Tests

```bash
# Install test dependencies
pip install pytest pytest-asyncio httpx

# Run the migration test suite
python -m pytest test_asgi_migration.py -v
```

### 2. Performance Testing

```bash
# Test concurrent requests
ab -n 1000 -c 10 http://localhost:8069/web/login

# Test WebSocket connections
wscat -c ws://localhost:8069/websocket
```

### 3. Database Performance

```bash
# Test async database operations
python -c "
import asyncio
from odoo.sql_db import db_connect

async def test():
    conn = await db_connect('mydb')
    async with conn.cursor() as cr:
        await cr.execute('SELECT COUNT(*) FROM res_users')
        result = await cr.fetchone()
        print(f'Users count: {result[0]}')

asyncio.run(test())
"
```

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure all async dependencies are installed
2. **Database Connection Issues**: Check asyncpg connection parameters
3. **Performance Issues**: Tune connection pool sizes and worker counts
4. **Compatibility Issues**: Some third-party modules may need updates

### Migration Checklist

- [ ] Remove psycopg2 dependencies
- [ ] Install asyncpg and ASGI dependencies
- [ ] Update database connection code
- [ ] Convert controllers to async
- [ ] Update server configuration
- [ ] Test all functionality
- [ ] Update deployment scripts
- [ ] Monitor performance

## Backward Compatibility

The migration maintains backward compatibility where possible:
- Existing sync controllers continue to work
- Database API provides compatibility layer
- Configuration files remain mostly unchanged
- Existing addons work with minimal changes

## Performance Expectations

Expected improvements:
- **Concurrent Requests**: 2-5x improvement in handling concurrent requests
- **Database Operations**: 20-40% improvement in database-heavy operations
- **Memory Usage**: 10-30% reduction in memory usage per request
- **Response Times**: 15-25% improvement in average response times

## Next Steps

1. **Monitor Performance**: Use APM tools to monitor the async application
2. **Update Addons**: Gradually convert custom addons to use async patterns
3. **Optimize Configuration**: Tune worker counts and connection pools
4. **Add WebSocket Features**: Implement real-time features using WebSocket support
