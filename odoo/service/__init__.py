# Part of Odoo. See LICENSE file for full copyright and licensing details.

from . import common
from . import db
from . import model
from . import server

# Create async_db as an alias to db since db.py already contains async functions
async_db = db

#.apidoc title: RPC Services

""" Classes of this module implement the network protocols that the
    OpenERP server uses to communicate with remote clients.

    Some classes are mostly utilities, whose API need not be visible to
    the average user/developer. Study them only if you are about to
    implement an extension to the network protocols, or need to debug some
    low-level behavior of the wire.
"""
