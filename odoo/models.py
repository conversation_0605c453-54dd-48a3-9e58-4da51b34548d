# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
Models - Backward Compatibility Module

This module provides backward compatibility by importing from the modular
components in the models package. The actual implementation has been split
into logical modules for better maintainability and async-ready architecture.

The modular structure includes:
- model_constants.py: Constants, regex patterns, and configurations
- model_utils.py: Utility functions
- model_exceptions.py: Error handling functions
- model_metaclass.py: MetaModel metaclass
- base_model.py: BaseModel class with core functionality
- model_types.py: Model, TransientModel, and AbstractModel classes

All components are designed to be async-ready and free of synchronous operations.
"""

# Import all components from the modular structure to maintain backward compatibility
from .models import *

# Ensure all main classes are available at module level for backward compatibility
__all__ = [
    'BaseModel',
    'Model',
    'TransientModel',
    'AbstractModel',
    'MetaModel',
    # Constants
    'AUTOINIT_RECALCULATE_STORED_FIELDS',
    'GC_UNLINK_LIMIT',
    'INSERT_BATCH_SIZE',
    'UPDATE_BATCH_SIZE',
    'PREFETCH_MAX',
    'LOG_ACCESS_COLUMNS',
    'MAGIC_COLUMNS',
    'READ_GROUP_TIME_GRANULARITY',
    'READ_GROUP_NUMBER_GRANULARITY',
    'READ_GROUP_ALL_TIME_GRANULARITY',
    'READ_GROUP_AGGREGATE',
    'READ_GROUP_DISPLAY_FORMAT',
    'SQL_DEFAULT',
    # Regex patterns
    'regex_alphanumeric',
    'regex_order',
    'regex_object_name',
    'regex_pg_name',
    'regex_field_agg',
    'regex_read_group_spec',
    'regex_private',
    # Utility functions
    'parse_read_group_spec',
    'check_object_name',
    'raise_on_invalid_object_name',
    'check_pg_name',
    'check_method_name',
    'check_property_field_value_name',
    'fix_import_export_id_paths',
    'to_company_ids',
    'check_company_domain_parent_of',
    'check_companies_domain_parent_of',
    'origin_ids',
    'expand_ids',
    'itemgetter_tuple',
    'is_definition_class',
    'is_registry_class',
    'OriginIds',
    # Exception handling
    'convert_pgerror_not_null',
    'convert_pgerror_unique',
    'convert_pgerror_constraint',
    'PGERROR_TO_OE',
]