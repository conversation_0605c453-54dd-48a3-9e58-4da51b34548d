# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
Async PostgreSQL connector for Odoo using asyncpg.
This replaces the synchronous psycopg2-based sql_db.py with async/await patterns.
"""
from __future__ import annotations

import asyncio
import logging
import os
import re
import threading
import time
import typing
import uuid
from contextlib import asynccontextmanager
from datetime import datetime, <PERSON><PERSON><PERSON>
from inspect import currentframe

import asyncpg
from werkzeug import urls

import odoo
from . import tools
from .tools import SQL
from .tools.func import frame_codeinfo, locked
from .tools.misc import Callbacks
from .sql_types import OperationalError, DatabaseError, ProgrammingError, NotSupportedError, IntegrityError, InterfaceError, InternalError, Warning

if typing.TYPE_CHECKING:
    from collections.abc import Iterable, Iterator
    T = typing.TypeVar('T')

_logger = logging.getLogger(__name__)

# Global SQL counter for debugging
sql_counter = 0

def real_time():
    """Return the current time in seconds since epoch."""
    return time.time()


class PoolError(Exception):
    """Exception raised when the connection pool is full."""
    pass


class BaseCursor:
    """Base class for async database cursors."""
    
    def __init__(self, connection, dbname):
        self._connection = connection
        self._dbname = dbname
        self._closed = False
        self.sql_log_count = 0
        self.__caller = frame_codeinfo(currentframe(), 2) if _logger.isEnabledFor(logging.DEBUG) else None

    @property
    def connection(self):
        return self._connection

    @property
    def dbname(self):
        return self._dbname

    async def close(self):
        """Close the cursor."""
        if not self._closed:
            self._closed = True

    async def __aenter__(self):
        """Async context manager entry."""
        return self

    async def __aexit__(self, exc_type, exc_value, traceback):
        """Async context manager exit."""
        try:
            if exc_type is None:
                await self.commit()
        finally:
            await self.close()


class Cursor(BaseCursor):
    """Cursor for PostgreSQL database operations using asyncpg."""
    
    def __init__(self, connection, dbname):
        super().__init__(connection, dbname)
        self._transaction = None

    async def execute(self, query, params=None, log_exceptions=True):
        """Execute a SQL query asynchronously."""
        global sql_counter

        if isinstance(query, SQL):
            assert params is None, "Unexpected parameters for SQL query object"
            query, params = query.code, query.params

        if params and not isinstance(params, (tuple, list, dict)):
            raise ValueError("SQL query parameters should be a tuple, list or dict; got %r" % (params,))

        start = real_time()
        try:
            params = params or None
            if isinstance(params, dict):
                # Convert dict params to list for asyncpg
                params = list(params.values())
            
            result = await self._connection.execute(query, *params if params else ())
        except Exception as e:
            if log_exceptions:
                _logger.error("bad query: %s\nERROR: %s", query, e)
            raise
        finally:
            delay = real_time() - start
            if _logger.isEnabledFor(logging.DEBUG):
                _logger.debug("[%.3f ms] query: %s", 1000 * delay, query)

        # Update counters
        self.sql_log_count += 1
        sql_counter += 1

        current_thread = threading.current_thread()
        if hasattr(current_thread, 'query_count'):
            current_thread.query_count += 1
            current_thread.query_time += delay

        # Optional hooks for performance and tracing analysis
        for hook in getattr(current_thread, 'query_hooks', ()):
            hook(self, query, params, start, delay)

        return result

    async def fetchall(self):
        """Fetch all remaining rows from the last query."""
        # In asyncpg, execute() returns the result directly
        # This method is kept for compatibility
        return []

    async def fetchone(self):
        """Fetch one row from the last query."""
        # In asyncpg, execute() returns the result directly
        # This method is kept for compatibility
        return None

    async def commit(self):
        """Commit the current transaction."""
        if self._transaction:
            await self._transaction.commit()
            self._transaction = None

    async def rollback(self):
        """Rollback the current transaction."""
        if self._transaction:
            await self._transaction.rollback()
            self._transaction = None

    async def begin(self):
        """Begin a new transaction."""
        if not self._transaction:
            self._transaction = self._connection.transaction()
            await self._transaction.start()


class TestCursor(Cursor):
    """Test cursor that provides transaction isolation for tests."""

    def __init__(self, real_cursor, lock, readonly=False, current_test=None):
        # Initialize with the real cursor's connection and dbname
        super().__init__(real_cursor._connection, real_cursor._dbname)
        self._real_cursor = real_cursor
        self._lock = lock
        self._readonly = readonly
        self._current_test = current_test
        self._savepoint_stack = []

    async def execute(self, query, params=None, log_exceptions=True):
        """Execute query through the real cursor."""
        return await self._real_cursor.execute(query, params, log_exceptions)

    async def commit(self):
        """In test mode, commit creates a savepoint."""
        if self._savepoint_stack:
            # Already in a savepoint, just flush
            pass
        else:
            # Create initial savepoint
            savepoint_name = f"test_savepoint_{len(self._savepoint_stack)}"
            await self._real_cursor.execute(f"SAVEPOINT {savepoint_name}")
            self._savepoint_stack.append(savepoint_name)

    async def rollback(self):
        """In test mode, rollback to the last savepoint."""
        if self._savepoint_stack:
            savepoint_name = self._savepoint_stack.pop()
            await self._real_cursor.execute(f"ROLLBACK TO SAVEPOINT {savepoint_name}")
        else:
            await self._real_cursor.rollback()

    async def close(self):
        """Close the test cursor."""
        # Clean up savepoints
        while self._savepoint_stack:
            savepoint_name = self._savepoint_stack.pop()
            try:
                await self._real_cursor.execute(f"RELEASE SAVEPOINT {savepoint_name}")
            except Exception:
                pass
        # Don't close the real cursor as it may be used by other tests
        self._closed = True


class Connection:
    """Wrapper for database connections."""

    def __init__(self, pool, dbname, connection_info):
        self._pool = pool
        self._dbname = dbname
        self._connection_info = connection_info
        self._connection = None
        self._closed = False

    async def _ensure_connection(self):
        """Ensure we have an active connection."""
        if not self._connection or self._connection.is_closed():
            self._connection = await self._pool.borrow_connection(self._dbname, self._connection_info)

    async def cursor(self):
        """Get a cursor for this connection."""
        await self._ensure_connection()
        return Cursor(self._connection, self._dbname)

    async def close(self):
        """Close the connection and return it to the pool."""
        if self._connection and not self._connection.is_closed():
            await self._pool.return_connection(self._connection)
        self._closed = True

    @property
    def closed(self):
        return self._closed or (self._connection and self._connection.is_closed())


class ConnectionPool:
    """Connection pool for PostgreSQL databases."""
    
    def __init__(self, maxconn=64, readonly=False):
        self._maxconn = max(maxconn, 1)
        self._readonly = readonly
        self._pools = {}  # dbname -> asyncpg.Pool
        self._lock = asyncio.Lock()

    async def borrow_connection(self, dbname, connection_info):
        """Borrow a connection from the pool."""
        async with self._lock:
            if dbname not in self._pools:
                # Create new pool for this database
                dsn = self._build_dsn(connection_info)
                self._pools[dbname] = await asyncpg.create_pool(
                    dsn,
                    min_size=1,
                    max_size=self._maxconn,
                    command_timeout=60
                )
            
            pool = self._pools[dbname]
            return await pool.acquire()

    async def return_connection(self, connection):
        """Return a connection to the pool."""
        # asyncpg pools handle this automatically when connection is released
        await connection.close()

    def _build_dsn(self, connection_info):
        """Build a DSN string from connection info."""
        if 'dsn' in connection_info:
            dsn = connection_info['dsn']
            # Ensure DSN has proper scheme for asyncpg
            if not dsn.startswith(('postgresql://', 'postgres://')):
                return f"postgresql://{dsn}"
            return dsn

        # Build PostgreSQL URL format for asyncpg
        host = connection_info.get('host', 'localhost')
        port = connection_info.get('port', '5432')
        database = connection_info.get('database', 'postgres')
        user = connection_info.get('user', 'postgres')
        password = connection_info.get('password', '')

        if password:
            return f"postgresql://{user}:{password}@{host}:{port}/{database}"
        else:
            return f"postgresql://{user}@{host}:{port}/{database}"

    async def close_all(self):
        """Close all connections in all pools."""
        async with self._lock:
            for pool in self._pools.values():
                await pool.close()
            self._pools.clear()


# Global connection pools
_Pool = None
_Pool_readonly = None


async def db_connect(to, allow_uri=False, readonly=False):
    """Database connection function."""
    global _Pool, _Pool_readonly

    maxconn = tools.config['db_maxconn']
    if _Pool is None and not readonly:
        _Pool = ConnectionPool(int(maxconn), readonly=False)
    if _Pool_readonly is None and readonly:
        _Pool_readonly = ConnectionPool(int(maxconn), readonly=True)

    db, info = connection_info_for(to, readonly)
    if not allow_uri and db != to:
        raise ValueError('URI connections not allowed')

    return Connection(_Pool_readonly if readonly else _Pool, db, info)


def connection_info_for(db_or_uri, readonly=False):
    """Parse database URI or name and return connection info."""
    if 'ODOO_PGAPPNAME' in os.environ:
        app_name = os.environ['ODOO_PGAPPNAME'].replace('{pid}', str(os.getpid()))[0:63]
    else:
        app_name = "odoo-%d" % os.getpid()
    
    if db_or_uri.startswith(('postgresql://', 'postgres://')):
        us = urls.url_parse(db_or_uri)
        if len(us.path) > 1:
            db_name = us.path[1:]
        elif us.username:
            db_name = us.username
        else:
            db_name = us.hostname
        return db_name, {'dsn': db_or_uri, 'application_name': app_name}

    connection_info = {'database': db_or_uri, 'application_name': app_name}
    for p in ('host', 'port', 'user', 'password', 'sslmode'):
        cfg = tools.config['db_' + p]
        if readonly:
            cfg = tools.config.get('db_replica_' + p, cfg)
        if cfg:
            connection_info[p] = cfg

    return db_or_uri, connection_info


async def close_db(db_name):
    """Close all connections to a specific database."""
    global _Pool, _Pool_readonly

    if _Pool:
        await _Pool.close_all()
    if _Pool_readonly:
        await _Pool_readonly.close_all()
