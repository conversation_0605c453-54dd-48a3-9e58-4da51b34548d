# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
WebSocket Bus Service for Odoo.
This module provides a real-time notification system using WebSockets
to replace or enhance longpolling mechanisms.
"""

import asyncio
import json
import logging
import time
from typing import Dict, List, Set, Any, Optional
from collections import defaultdict

from .websocket_controllers import WebSocketController, websocket_route, register_websocket_controller
from .websocket import websocket_manager, WebSocketConnection

_logger = logging.getLogger(__name__)


class WebSocketBusService:
    """
    WebSocket-based bus service for real-time notifications.
    
    This service manages channels, subscriptions, and message broadcasting
    for real-time communication in Odoo.
    """
    
    def __init__(self):
        self.channels: Dict[str, Set[str]] = defaultdict(set)  # channel -> connection_ids
        self.connection_channels: Dict[str, Set[str]] = defaultdict(set)  # connection_id -> channels
        self.message_queue: Dict[str, List[Dict]] = defaultdict(list)  # channel -> messages
        self.last_message_id = 0
        
    def subscribe(self, connection_id: str, channels: List[str]):
        """Subscribe a connection to multiple channels."""
        for channel in channels:
            self.channels[channel].add(connection_id)
            self.connection_channels[connection_id].add(channel)
            _logger.debug(f"Connection {connection_id} subscribed to channel {channel}")
            
    def unsubscribe(self, connection_id: str, channels: List[str] = None):
        """Unsubscribe a connection from channels."""
        if channels is None:
            # Unsubscribe from all channels
            channels = list(self.connection_channels[connection_id])
            
        for channel in channels:
            self.channels[channel].discard(connection_id)
            self.connection_channels[connection_id].discard(channel)
            
            # Clean up empty channels
            if not self.channels[channel]:
                del self.channels[channel]
                
        # Clean up empty connection entry
        if not self.connection_channels[connection_id]:
            del self.connection_channels[connection_id]
            
        _logger.debug(f"Connection {connection_id} unsubscribed from channels {channels}")
        
    async def broadcast(self, channel: str, message: Dict[str, Any], exclude_connection: str = None):
        """Broadcast a message to all subscribers of a channel."""
        if channel not in self.channels:
            return
            
        self.last_message_id += 1
        message_with_id = {
            'id': self.last_message_id,
            'channel': channel,
            'timestamp': time.time(),
            **message
        }
        
        # Store message in queue for late subscribers
        self.message_queue[channel].append(message_with_id)
        
        # Keep only last 100 messages per channel
        if len(self.message_queue[channel]) > 100:
            self.message_queue[channel] = self.message_queue[channel][-100:]
            
        # Broadcast to all subscribers
        disconnected = []
        for connection_id in self.channels[channel].copy():
            if connection_id == exclude_connection:
                continue
                
            if connection_id in websocket_manager.connections:
                connection = websocket_manager.connections[connection_id]
                try:
                    await connection.send_json({
                        'type': 'bus_message',
                        **message_with_id
                    })
                except Exception as e:
                    _logger.warning(f"Failed to send bus message to {connection_id}: {e}")
                    disconnected.append(connection_id)
                    
        # Clean up disconnected connections
        for connection_id in disconnected:
            self.unsubscribe(connection_id)
            
    def get_messages(self, channel: str, last_id: int = 0) -> List[Dict]:
        """Get messages from a channel since last_id."""
        if channel not in self.message_queue:
            return []
            
        return [msg for msg in self.message_queue[channel] if msg['id'] > last_id]
        
    def get_channel_stats(self) -> Dict[str, Any]:
        """Get statistics about channels and subscriptions."""
        return {
            'total_channels': len(self.channels),
            'total_subscriptions': sum(len(connections) for connections in self.channels.values()),
            'channels': {
                channel: len(connections)
                for channel, connections in self.channels.items()
            }
        }


# Global bus service instance
bus_service = WebSocketBusService()


@register_websocket_controller
class BusWebSocketController(WebSocketController):
    """WebSocket controller for the bus service."""
    
    @websocket_route('/ws/bus', auth='user')
    async def bus_handler(self, connection: WebSocketConnection):
        """Main bus WebSocket endpoint."""
        await connection.send_json({
            'type': 'bus_connected',
            'connection_id': connection.connection_id,
            'message': 'Connected to bus service'
        })
        
        try:
            while not connection.closed:
                message = await connection.receive_message()
                
                if message['type'] == 'websocket.receive':
                    if 'text' in message:
                        try:
                            data = json.loads(message['text'])
                            await self._handle_bus_message(connection, data)
                        except json.JSONDecodeError:
                            await connection.send_json({
                                'type': 'error',
                                'message': 'Invalid JSON format'
                            })
                            
                elif message['type'] == 'websocket.disconnect':
                    break
                    
        except Exception as e:
            _logger.error(f"Bus handler error: {e}")
        finally:
            # Clean up subscriptions
            bus_service.unsubscribe(connection.connection_id)
            if not connection.closed:
                await connection.close()
                
    async def _handle_bus_message(self, connection: WebSocketConnection, data: Dict[str, Any]):
        """Handle incoming bus messages."""
        message_type = data.get('type')
        
        if message_type == 'ping':
            connection.last_ping = time.time()
            await connection.send_json({
                'type': 'pong',
                'timestamp': time.time()
            })
            
        elif message_type == 'subscribe':
            channels = data.get('channels', [])
            if isinstance(channels, str):
                channels = [channels]
                
            # Validate channel access
            allowed_channels = await self._get_allowed_channels(connection, channels)
            bus_service.subscribe(connection.connection_id, allowed_channels)
            
            await connection.send_json({
                'type': 'subscribed',
                'channels': allowed_channels
            })
            
            # Send recent messages for subscribed channels
            for channel in allowed_channels:
                last_id = data.get('last_id', 0)
                recent_messages = bus_service.get_messages(channel, last_id)
                for msg in recent_messages:
                    await connection.send_json({
                        'type': 'bus_message',
                        **msg
                    })
                    
        elif message_type == 'unsubscribe':
            channels = data.get('channels', [])
            if isinstance(channels, str):
                channels = [channels]
            bus_service.unsubscribe(connection.connection_id, channels)
            
            await connection.send_json({
                'type': 'unsubscribed',
                'channels': channels
            })
            
        elif message_type == 'publish':
            # Allow publishing to certain channels
            channel = data.get('channel')
            message = data.get('message', {})
            
            if await self._can_publish_to_channel(connection, channel):
                await bus_service.broadcast(channel, message, connection.connection_id)
                await connection.send_json({
                    'type': 'published',
                    'channel': channel
                })
            else:
                await connection.send_json({
                    'type': 'error',
                    'message': f'Not allowed to publish to channel: {channel}'
                })
                
        else:
            await connection.send_json({
                'type': 'error',
                'message': f'Unknown message type: {message_type}'
            })
            
    async def _get_allowed_channels(self, connection: WebSocketConnection, channels: List[str]) -> List[str]:
        """Filter channels based on user permissions."""
        allowed = []
        
        for channel in channels:
            if await self._can_subscribe_to_channel(connection, channel):
                allowed.append(channel)
            else:
                _logger.warning(f"User {connection.user_id} denied access to channel {channel}")
                
        return allowed
        
    async def _can_subscribe_to_channel(self, connection: WebSocketConnection, channel: str) -> bool:
        """Check if user can subscribe to a channel."""
        # Public channels
        if channel.startswith('public_'):
            return True
            
        # User-specific channels
        if channel.startswith(f'user_{connection.user_id}'):
            return True
            
        # Database-specific channels
        if channel.startswith(f'db_{connection.db_name}'):
            return True
            
        # Admin channels
        if channel.startswith('admin_'):
            from .websocket import check_websocket_auth
            return check_websocket_auth('admin', connection)
            
        # Default: deny access
        return False
        
    async def _can_publish_to_channel(self, connection: WebSocketConnection, channel: str) -> bool:
        """Check if user can publish to a channel."""
        # User can publish to their own channels
        if channel.startswith(f'user_{connection.user_id}'):
            return True
            
        # Admin can publish to admin channels
        if channel.startswith('admin_'):
            from .websocket import check_websocket_auth
            return check_websocket_auth('admin', connection)
            
        # Default: deny publishing
        return False


@register_websocket_controller
class NotificationWebSocketController(WebSocketController):
    """WebSocket controller for notifications."""
    
    @websocket_route('/ws/notifications', auth='user')
    async def notifications_handler(self, connection: WebSocketConnection):
        """Handle user notifications via WebSocket."""
        # Subscribe to user-specific notification channels
        user_channels = [
            f'user_{connection.user_id}',
            f'db_{connection.db_name}',
            'public_notifications'
        ]
        
        bus_service.subscribe(connection.connection_id, user_channels)
        
        await connection.send_json({
            'type': 'notifications_ready',
            'channels': user_channels,
            'connection_id': connection.connection_id
        })
        
        try:
            while not connection.closed:
                message = await connection.receive_message()
                
                if message['type'] == 'websocket.receive':
                    if 'text' in message:
                        try:
                            data = json.loads(message['text'])
                            
                            if data.get('type') == 'ping':
                                connection.last_ping = time.time()
                                await connection.send_json({
                                    'type': 'pong',
                                    'timestamp': time.time()
                                })
                            elif data.get('type') == 'mark_read':
                                # Handle notification read status
                                notification_id = data.get('notification_id')
                                if notification_id:
                                    await self._mark_notification_read(connection, notification_id)
                                    
                        except json.JSONDecodeError:
                            await connection.send_json({
                                'type': 'error',
                                'message': 'Invalid JSON format'
                            })
                            
                elif message['type'] == 'websocket.disconnect':
                    break
                    
        except Exception as e:
            _logger.error(f"Notifications handler error: {e}")
        finally:
            bus_service.unsubscribe(connection.connection_id)
            if not connection.closed:
                await connection.close()
                
    async def _mark_notification_read(self, connection: WebSocketConnection, notification_id: int):
        """Mark a notification as read."""
        try:
            import odoo
            if connection.db_name:
                registry = odoo.modules.registry.Registry(connection.db_name)
                with registry.cursor() as cr:
                    env = odoo.api.Environment(cr, connection.user_id, {})
                    # This would integrate with Odoo's notification system
                    # For now, just acknowledge
                    await connection.send_json({
                        'type': 'notification_read',
                        'notification_id': notification_id
                    })
        except Exception as e:
            _logger.error(f"Error marking notification as read: {e}")


# Helper functions for integration with Odoo's existing systems
async def send_notification(user_id: int, db_name: str, notification: Dict[str, Any]):
    """Send a notification to a specific user via WebSocket."""
    channel = f'user_{user_id}'
    await bus_service.broadcast(channel, {
        'type': 'notification',
        'user_id': user_id,
        'db_name': db_name,
        **notification
    })


async def send_broadcast(db_name: str, message: Dict[str, Any]):
    """Send a broadcast message to all users in a database."""
    channel = f'db_{db_name}'
    await bus_service.broadcast(channel, {
        'type': 'broadcast',
        'db_name': db_name,
        **message
    })


async def send_public_notification(message: Dict[str, Any]):
    """Send a public notification to all connected users."""
    await bus_service.broadcast('public_notifications', {
        'type': 'public_notification',
        **message
    })


class OdooBusIntegration:
    """Integration layer between WebSocket bus and Odoo's existing bus system."""

    def __init__(self):
        self.enabled = False

    async def start(self):
        """Start the integration service."""
        try:
            # Check if bus.bus model exists
            import odoo
            from odoo.service.db import exp_list

            # Get list of databases
            databases = await exp_list()

            for db_name in databases:
                try:
                    registry = odoo.modules.registry.Registry(db_name)
                    with registry.cursor() as cr:
                        env = odoo.api.Environment(cr, odoo.SUPERUSER_ID, {})
                        if 'bus.bus' in env:
                            self.enabled = True
                            _logger.info(f"Bus integration enabled for database {db_name}")
                            # Start polling for bus messages
                            asyncio.create_task(self._poll_bus_messages(db_name))
                except Exception as e:
                    _logger.warning(f"Could not enable bus integration for {db_name}: {e}")

        except Exception as e:
            _logger.error(f"Failed to start bus integration: {e}")

    async def _poll_bus_messages(self, db_name: str):
        """Poll for messages from Odoo's bus.bus model."""
        last_id = 0

        while True:
            try:
                import odoo
                registry = odoo.modules.registry.Registry(db_name)
                with registry.cursor() as cr:
                    env = odoo.api.Environment(cr, odoo.SUPERUSER_ID, {})

                    if 'bus.bus' in env:
                        # Get new bus messages
                        messages = env['bus.bus'].search([
                            ('id', '>', last_id)
                        ], order='id')

                        for message in messages:
                            # Convert bus message to WebSocket format
                            await self._forward_bus_message(db_name, message)
                            last_id = message.id

                await asyncio.sleep(1)  # Poll every second

            except Exception as e:
                _logger.error(f"Error polling bus messages for {db_name}: {e}")
                await asyncio.sleep(5)  # Wait longer on error

    async def _forward_bus_message(self, db_name: str, bus_message):
        """Forward a bus.bus message to WebSocket subscribers."""
        try:
            channel = bus_message.channel
            message_data = {
                'type': 'bus_notification',
                'bus_id': bus_message.id,
                'channel': channel,
                'message': bus_message.message,
                'db_name': db_name
            }

            # Determine WebSocket channel based on bus channel
            ws_channel = self._map_bus_channel_to_ws(channel, db_name)

            await bus_service.broadcast(ws_channel, message_data)

        except Exception as e:
            _logger.error(f"Error forwarding bus message: {e}")

    def _map_bus_channel_to_ws(self, bus_channel: str, db_name: str) -> str:
        """Map Odoo bus channel to WebSocket channel."""
        # Map specific patterns
        if bus_channel.startswith('res.users'):
            # User-specific channel
            user_id = bus_channel.split(',')[-1] if ',' in bus_channel else None
            if user_id:
                return f'user_{user_id}'

        # Default to database-specific channel
        return f'db_{db_name}'

    async def send_to_odoo_bus(self, db_name: str, channel: str, message: Any):
        """Send a message to Odoo's bus.bus system."""
        try:
            import odoo
            registry = odoo.modules.registry.Registry(db_name)
            with registry.cursor() as cr:
                env = odoo.api.Environment(cr, odoo.SUPERUSER_ID, {})

                if 'bus.bus' in env:
                    env['bus.bus']._sendone(channel, message)
                    cr.commit()

        except Exception as e:
            _logger.error(f"Error sending to Odoo bus: {e}")


# Global bus integration instance
bus_integration = OdooBusIntegration()


async def start_bus_integration():
    """Start the bus integration service."""
    await bus_integration.start()
