# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
Async request/response handling for Odoo ASGI application.
This module provides async versions of the HTTP request and response classes.
"""

import asyncio
import contextlib
import functools
import logging
from datetime import datetime, timedelta

import werkzeug.exceptions
import werkzeug.wrappers
import werkzeug.datastructures
import werkzeug.local
from starlette.requests import Request as StarletteRequest
from starlette.responses import Response as StarletteResponse

from odoo.tools.facade import Proxy, ProxyFunc, ProxyAttr
from odoo.tools._vendor.useragents import UserAgent

from .utils import DEFAULT_MAX_CONTENT_LENGTH

_logger = logging.getLogger(__name__)

# Request stack using asyncio context variables
import contextvars
_request_context = contextvars.ContextVar('request', default=None)

# Legacy request stack for backward compatibility
_request_stack = werkzeug.local.LocalStack()


class FutureResponse:
    """FutureResponse for handling response headers and cookies."""

    def __init__(self):
        self.headers = Headers()
        self.cookies = {}
        self.status = None


class Headers:
    """Headers for response header management."""

    def __init__(self):
        self._headers = {}

    def set(self, key, value):
        """Set a header value."""
        self._headers[key] = value

    def get(self, key, default=None):
        """Get a header value."""
        return self._headers.get(key, default)

    def __setitem__(self, key, value):
        self.set(key, value)

    def __getitem__(self, key):
        return self._headers[key]

    def items(self):
        return self._headers.items()


@contextlib.asynccontextmanager
async def borrow_request():
    """Get the current request from context."""
    req = _request_context.get()
    try:
        yield req
    finally:
        pass  # Context variables handle cleanup automatically


class HTTPRequest:
    """Wrapper for HTTP requests."""
    
    def __init__(self, starlette_request: StarletteRequest):
        self._starlette_request = starlette_request
        self._body = None
        self._form = None
        self._files = None

    @property
    def method(self):
        return self._starlette_request.method

    @property
    def url(self):
        return str(self._starlette_request.url)

    @property
    def url_root(self):
        """Get the URL root (scheme + netloc + /)."""
        url = self._starlette_request.url
        return f"{url.scheme}://{url.netloc}/"

    @property
    def path(self):
        return self._starlette_request.url.path

    @property
    def query_string(self):
        return self._starlette_request.url.query

    @property
    def headers(self):
        return dict(self._starlette_request.headers)

    @property
    def remote_addr(self):
        return self._starlette_request.client.host if self._starlette_request.client else ''

    @property
    def environ(self):
        """Create WSGI-like environ dict for compatibility."""
        if not hasattr(self, '_environ'):
            self._environ = {
                'REQUEST_METHOD': self.method,
                'PATH_INFO': self.path,
                'QUERY_STRING': self.query_string,
                'CONTENT_TYPE': self.headers.get('content-type', ''),
                'CONTENT_LENGTH': self.headers.get('content-length', ''),
                'SERVER_NAME': self._starlette_request.url.hostname or 'localhost',
                'SERVER_PORT': str(self._starlette_request.url.port or 80),
                'wsgi.url_scheme': self._starlette_request.url.scheme,
                'REMOTE_ADDR': self.remote_addr,
                'HTTP_HOST': self.headers.get('host', ''),
                'HTTP_USER_AGENT': self.headers.get('user-agent', ''),
                'HTTP_ACCEPT': self.headers.get('accept', ''),
                'HTTP_ACCEPT_LANGUAGE': self.headers.get('accept-language', ''),
                'HTTP_ACCEPT_ENCODING': self.headers.get('accept-encoding', ''),
                'HTTP_COOKIE': self.headers.get('cookie', ''),
                'HTTP_REFERER': self.headers.get('referer', ''),
            }
            # Add all HTTP headers with HTTP_ prefix
            for key, value in self.headers.items():
                if key.lower() not in ['content-type', 'content-length']:
                    environ_key = 'HTTP_' + key.upper().replace('-', '_')
                    if environ_key not in self._environ:
                        self._environ[environ_key] = value
        return self._environ

    async def get_body(self):
        """Get request body asynchronously."""
        if self._body is None:
            self._body = await self._starlette_request.body()
        return self._body

    async def get_form(self):
        """Get form data asynchronously."""
        if self._form is None:
            form_data = await self._starlette_request.form()
            self._form = dict(form_data)
        return self._form

    async def get_json(self):
        """Get JSON data asynchronously."""
        return await self._starlette_request.json()

    def get_cookie(self, name, default=None):
        """Get cookie value."""
        return self._starlette_request.cookies.get(name, default)

    @property
    def args(self):
        """Get query parameters as a dict-like object."""
        from urllib.parse import parse_qs
        query_params = parse_qs(self.query_string)
        # Convert lists to single values for compatibility
        return {k: v[0] if len(v) == 1 else v for k, v in query_params.items()}

    @property
    def form(self):
        """Get form data - this is a sync property, use get_form() for async."""
        # Return cached form data if available, otherwise empty dict
        return self._form or {}

    @property
    def files(self):
        """Get uploaded files - this is a sync property."""
        # For sync compatibility, return empty dict
        # Real file data should be accessed via async methods
        return {}

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        pass


class Request:
    """Odoo's Request class."""

    def __init__(self, httprequest: HTTPRequest):
        self.httprequest = httprequest
        self.db = None
        self.session = None
        self.dispatcher = None
        self._context = {}
        self.params = {}  # Will be set by dispatcher
        self.registry = None
        self.env = None
        self.future_response = FutureResponse()

    async def _post_init(self):
        """Async initialization after request creation."""
        # Pre-load form data for sync compatibility
        await self._init_form_data()
        # Initialize session, database connection, etc.
        await self._init_session()
        await self._init_database()
        await self._init_dispatcher()

    async def _init_form_data(self):
        """Pre-load form data for sync access."""
        if self.httprequest.method in ['POST', 'PUT', 'PATCH']:
            try:
                # Pre-load form data so it's available for sync access
                await self.httprequest.get_form()
            except Exception:
                # If form parsing fails, just continue with empty form
                pass

    async def _init_session(self):
        """Initialize session asynchronously."""
        from . import root

        # Get session from session store
        session_id = None
        cookie_header = self.httprequest.headers.get('cookie', '')
        if cookie_header:
            # Parse session ID from cookie
            import http.cookies
            cookies = http.cookies.SimpleCookie()
            cookies.load(cookie_header)
            if 'session_id' in cookies:
                session_id = cookies['session_id'].value

        # Only pass valid session IDs to the session store
        if session_id and root.session_store.is_valid_key(session_id):
            self.session = root.session_store.get(session_id)
        else:
            self.session = root.session_store.new()

    async def _init_database(self):
        """Initialize database connection asynchronously."""
        # Determine database from session or request
        self.db = None
        if self.session:
            self.db = getattr(self.session, 'db', None)

        # If no database in session, try to get from query parameters
        if not self.db:
            import urllib.parse
            query_params = urllib.parse.parse_qs(self.httprequest.query_string)
            if 'db' in query_params:
                self.db = query_params['db'][0]

    async def _init_dispatcher(self):
        """Initialize request dispatcher asynchronously."""
        # Dispatcher will be set up when needed in _serve_nodb or _serve_db
        pass

    async def _serve_static(self):
        """Serve static files asynchronously."""
        # TODO: Implement async static file serving
        return Response("Static file", status=200)

    async def _serve_db(self):
        """Serve database-backed requests asynchronously."""
        # TODO: Implement async database request handling
        return Response("Database response", status=200)

    async def _serve_nodb(self):
        """Serve requests without database asynchronously."""
        # Import here to avoid circular imports
        from . import root
        from .dispatchers import AsyncHttpDispatcher
        import werkzeug.exceptions
        import asyncio

        try:
            # Match against no-database routing map
            router = root.nodb_routing_map.bind_to_environ(self.httprequest.environ)
            rule, args = router.match(return_rule=True)

            # Set up async dispatcher
            self.dispatcher = AsyncHttpDispatcher(self)
            self.dispatcher.pre_dispatch(rule, args)

            # Dispatch to the controller (always await since dispatcher handles both sync and async)
            response = await self.dispatcher.dispatch(rule.endpoint, args)
            self.dispatcher.post_dispatch(response)

            # Convert response to Response if needed
            if isinstance(response, Response):
                return response
            elif hasattr(response, 'to_starlette_response'):
                return response
            elif hasattr(response, 'status_code'):
                # Handle Werkzeug response
                return Response(
                    content=response.get_data(as_text=True),
                    status=response.status_code,
                    headers=dict(response.headers)
                )
            elif hasattr(response, 'headers') and hasattr(response, 'status'):
                # Handle Response object
                content = response.get_data(as_text=True) if hasattr(response, 'get_data') else str(response)
                return Response(
                    content=content,
                    status=getattr(response, 'status_code', response.status),
                    headers=dict(response.headers) if hasattr(response.headers, 'items') else {}
                )
            else:
                # Handle string response or other types
                return Response(str(response), status=200)

        except werkzeug.exceptions.HTTPException as e:
            # Handle HTTP exceptions (like redirects from abort())
            if hasattr(e, 'response') and e.response:
                # Convert werkzeug response to Response
                response = e.response
                content = response.get_data(as_text=True) if hasattr(response, 'get_data') else ''
                status = getattr(response, 'status_code', getattr(e, 'code', 500))
                headers = dict(response.headers) if hasattr(response, 'headers') else {}
                return Response(content=content, status=status, headers=headers)
            else:
                # Handle other HTTP exceptions
                return Response(str(e), status=getattr(e, 'code', 500))
        except werkzeug.exceptions.NotFound:
            return Response("Not Found", status=404)
        except Exception:
            import logging
            _logger = logging.getLogger(__name__)
            _logger.error("Error in _serve_nodb", exc_info=True)
            return Response("Internal Server Error", status=500)

    def reroute(self, path, query_string=None):
        """Reroute the request to a different path."""
        # TODO: Implement rerouting for async requests
        pass

    def redirect(self, location, code=303, local=True):
        """Create a redirect response."""
        if local:
            # Ensure the redirect is to the same host
            from urllib.parse import urlparse
            parsed = urlparse(location)
            if parsed.netloc and parsed.netloc != self.httprequest.headers.get('host', ''):
                raise ValueError("Redirect to external host not allowed when local=True")

        # Return a werkzeug redirect response for compatibility
        import werkzeug.utils
        import werkzeug.wrappers
        werkzeug_response = werkzeug.utils.redirect(location, code, Response=werkzeug.wrappers.Response)

        # Convert to our Response format
        return Response(
            content=werkzeug_response.get_data(as_text=True),
            status=werkzeug_response.status_code,
            headers=dict(werkzeug_response.headers),
            content_type=werkzeug_response.content_type
        )

    def redirect_query(self, location, query=None, code=303, local=True):
        """Create a redirect response with query parameters."""
        if query:
            from urllib.parse import urlencode, urlparse, urlunparse, parse_qs
            parsed = urlparse(location)
            query_dict = parse_qs(parsed.query)
            query_dict.update(query)
            new_query = urlencode(query_dict, doseq=True)
            location = urlunparse((
                parsed.scheme, parsed.netloc, parsed.path,
                parsed.params, new_query, parsed.fragment
            ))
        return self.redirect(location, code, local)

    def get_http_params(self):
        """
        Extract and return HTTP parameters from the request.
        """
        params = dict(self.httprequest.args)
        params.update(dict(self.httprequest.form))
        params.update(dict(self.httprequest.files))
        return params

    def _save_session(self):
        """
        Save the current session if it's dirty.
        """
        # Import here to avoid circular imports
        from . import root

        if self.session and self.session.is_dirty:
            if self.session.should_rotate:
                root.session_store.rotate(self.session, self.env)
            else:
                root.session_store.save(self.session)

    def _inject_future_response(self, response):
        """Inject future response headers into the actual response."""
        if hasattr(response, 'headers') and hasattr(self.future_response, 'headers'):
            for key, value in self.future_response.headers.items():
                response.headers[key] = value


class Response:
    """Response wrapper."""
    
    def __init__(self, content, status=200, headers=None, content_type='text/html'):
        self.content = content
        self.status = status
        self.headers = headers or {}
        self.content_type = content_type

    def to_starlette_response(self):
        """Convert to Starlette response."""
        if isinstance(self.content, str):
            content = self.content.encode('utf-8')
        else:
            content = self.content

        headers = dict(self.headers)
        headers['content-type'] = self.content_type

        return StarletteResponse(
            content=content,
            status_code=self.status,
            headers=headers
        )

    async def __call__(self, scope, receive, send):
        """ASGI response interface."""
        response = self.to_starlette_response()
        await response(scope, receive, send)





class ResponseCacheControl:
    """Cache control for async responses."""
    
    def __init__(self):
        self.max_age = None
        self.no_cache = False
        self.no_store = False

    def to_header(self):
        """Convert to cache-control header value."""
        parts = []
        if self.max_age is not None:
            parts.append(f'max-age={self.max_age}')
        if self.no_cache:
            parts.append('no-cache')
        if self.no_store:
            parts.append('no-store')
        return ', '.join(parts)


class ResponseStream:
    """Streaming response for async operations."""
    
    def __init__(self, generator):
        self.generator = generator

    async def stream(self):
        """Stream response data asynchronously."""
        async for chunk in self.generator:
            yield chunk


# Request context management
def get_request():
    """Get the current request from context."""
    return _request_context.get()


def set_request(request):
    """Set the current request in context."""
    _request_context.set(request)


async def abort(status_code, description=None):
    """Abort function."""
    raise werkzeug.exceptions.HTTPException(
        response=Response(
            description or f"HTTP {status_code}",
            status=status_code
        )
    )


# Compatibility layer for existing code
class RequestProxy:
    """Proxy to provide sync-like interface for requests."""
    
    def __init__(self):
        self._current_request = None

    def __getattr__(self, name):
        request = get_request()
        if request is None:
            raise RuntimeError("No request in current context")
        return getattr(request, name)


# Global request proxy for backward compatibility
request = RequestProxy()
