# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
Async controllers for Odoo ASGI application.
This module provides async versions of HTTP controllers and route decorators.
"""

import asyncio
import collections
import functools
import inspect
import logging

from odoo.tools import unique
from odoo.tools.func import filter_kwargs

_logger = logging.getLogger(__name__)


class Controller:
    """
    Base class for HTTP controllers that can serve content over HTTP
    using async/await patterns. Each class inheriting from Controller
    can use the route decorator to route matching incoming web requests
    to decorated async methods.

    Example:
        class GreetingController(Controller):
            @route('/greet', type='http', auth='public')
            async def greeting(self):
                # Async database operations
                result = await some_db_operation()
                return f'Hello {result}'
    """
    children_classes = collections.defaultdict(list)

    def __init_subclass__(cls, **kwargs):
        super().__init_subclass__(**kwargs)
        # Register the controller class
        module_parts = cls.__module__.split('.')
        if len(module_parts) >= 3 and module_parts[:2] == ['odoo', 'addons']:
            module_name = module_parts[2]
        else:
            module_name = ''
        Controller.children_classes[module_name].append(cls)


def route(route=None, **kw):
    """
    Decorator to expose controller methods as HTTP endpoints.

    This decorator marks methods as route handlers and configures
    their routing parameters.

    :param str route: URL pattern for the route
    :param str type: Request type ('http' or 'json')
    :param str auth: Authentication requirement ('public', 'user', 'admin')
    :param list methods: HTTP methods allowed (['GET', 'POST', etc.])
    :param bool cors: Enable CORS support
    :param bool csrf: Enable CSRF protection
    :param bool save_session: Save session after request
    :param bool readonly: Mark as read-only operation
    """
    
    def decorator(func):
        if not asyncio.iscoroutinefunction(func):
            raise ValueError(f"Function {func.__name__} must be async to use route decorator")

        # Store routing information
        routing = kw.copy()
        routing['routes'] = [route] if route else []

        # Mark as route
        func.routing = routing
        func.original_routing = routing
        func._route = True
        
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # Pre-processing for async routes
            try:
                result = await func(*args, **kwargs)
                return result
            except Exception as e:
                _logger.error(f"Error in async route {func.__name__}: {e}", exc_info=True)
                raise
        
        wrapper.routing = routing
        wrapper.original_routing = routing
        wrapper._route = True
        wrapper.original_func = func
        
        return wrapper
    
    return decorator


def json_route(route=None, **kw):
    """Decorator for JSON routes."""
    kw['type'] = 'json'
    return route(route, **kw)


def http_route(route=None, **kw):
    """Decorator for HTTP routes."""
    kw['type'] = 'http'
    return route(route, **kw)


class RouteRegistry:
    """Registry for routes."""
    
    def __init__(self):
        self._routes = {}
        self._controllers = {}

    def register_controller(self, controller_class):
        """Register an async controller class."""
        controller_name = controller_class.__name__
        self._controllers[controller_name] = controller_class
        
        # Register all routes from the controller
        for method_name in dir(controller_class):
            method = getattr(controller_class, method_name)
            if hasattr(method, '_route') and hasattr(method, 'routing'):
                route_pattern = method.routing.get('route')
                if route_pattern:
                    self._routes[route_pattern] = {
                        'controller': controller_class,
                        'method': method,
                        'routing': method.routing
                    }

    def get_route(self, path):
        """Get route handler for a given path."""
        return self._routes.get(path)

    def get_all_routes(self):
        """Get all registered routes."""
        return self._routes.copy()


# Global route registry
route_registry = RouteRegistry()


def register_controller(controller_class):
    """Register a controller class globally."""
    route_registry.register_controller(controller_class)
    return controller_class


class Dispatcher:
    """Request dispatcher."""
    
    def __init__(self, request):
        self.request = request

    async def dispatch(self, endpoint, args):
        """Dispatch request to endpoint."""
        try:
            if hasattr(endpoint, '_route'):
                # This is a route
                if inspect.ismethod(endpoint):
                    result = await endpoint(*args)
                else:
                    # Create controller instance and call method
                    controller = endpoint.__self__.__class__()
                    method = getattr(controller, endpoint.__name__)
                    result = await method(*args)
                return result
            else:
                # Fallback to sync route (wrapped in async)
                if inspect.ismethod(endpoint):
                    result = endpoint(*args)
                else:
                    controller = endpoint.__self__.__class__()
                    method = getattr(controller, endpoint.__name__)
                    result = method(*args)
                return result
        except Exception as e:
            _logger.error(f"Error dispatching to {endpoint}: {e}", exc_info=True)
            raise

    async def handle_error(self, exc):
        """Handle errors in request processing."""
        # TODO: Implement error handling
        from .request_response import Response
        return Response(f"Error: {exc}", status=500)


def generate_routing_rules(modules, nodb_only=False):
    """Generate routing rules for controllers."""
    import functools
    from odoo.tools.misc import unique



    def is_valid(cls):
        """ Determine if the class is defined in an addon. """
        path = cls.__module__.split('.')
        return path[:2] == ['odoo', 'addons'] and path[2] in modules

    def get_leaf_classes(cls):
        """
        Find the classes that have no child and that have ``cls`` as
        ancestor.
        """
        result = []
        for subcls in cls.__subclasses__():
            if is_valid(subcls):
                result.extend(get_leaf_classes(subcls))
        if not result and is_valid(cls):
            result.append(cls)
        return result

    def build_controllers():
        """
        Create dummy controllers that inherit only from the controllers
        defined at the given ``modules`` (often system wide modules or
        installed modules). Modules in this context are Odoo addons.
        """
        # Controllers defined outside of odoo addons are outside of the
        # controller inheritance/extension mechanism.
        yield from (ctrl() for ctrl in Controller.children_classes.get('', []))

        # Controllers defined inside of odoo addons can be extended in
        # other installed addons. Rebuild the class inheritance here.
        highest_controllers = []
        for module in modules:
            controllers = Controller.children_classes.get(module, [])
            highest_controllers.extend(controllers)

        for top_ctrl in highest_controllers:
            leaf_controllers = list(unique(get_leaf_classes(top_ctrl)))

            name = top_ctrl.__name__
            if leaf_controllers != [top_ctrl]:
                name += ' (extended by %s)' %  ', '.join(
                    bot_ctrl.__name__
                    for bot_ctrl in leaf_controllers
                    if bot_ctrl is not top_ctrl
                )

            Ctrl = type(name, tuple(reversed(leaf_controllers)), {})
            yield Ctrl()

    for ctrl in build_controllers():
        for method_name, method in inspect.getmembers(ctrl, inspect.ismethod):

            # Skip this method if it is not @route decorated anywhere in
            # the hierarchy
            def is_method_a_route(cls):
                return getattr(getattr(cls, method_name, None), 'original_routing', None) is not None
            if not any(map(is_method_a_route, type(ctrl).mro())):
                continue

            merged_routing = {
                'auth': 'user',
                'methods': None,
                'routes': [],
            }

            for cls in unique(reversed(type(ctrl).mro()[:-2])):  # ancestors first
                if method_name not in cls.__dict__:
                    continue
                submethod = getattr(cls, method_name)

                if not hasattr(submethod, 'original_routing'):
                    _logger.warning("The endpoint %s is not decorated by @route(), decorating it myself.", f'{cls.__module__}.{cls.__name__}.{method_name}')
                    submethod = route()(submethod)

                merged_routing.update(submethod.original_routing)

            if not merged_routing['routes']:
                _logger.warning("%s is a controller endpoint without any route, skipping.", f'{type(ctrl).__module__}.{type(ctrl).__name__}.{method_name}')
                continue

            if nodb_only and merged_routing['auth'] != "none":
                continue



            for url in merged_routing['routes']:
                # Handle nested lists of URLs (some routes have multiple URLs)
                if isinstance(url, list):
                    for sub_url in url:
                        # duplicates the function (partial) with a copy of the
                        # original __dict__ (update_wrapper) to keep a reference
                        # to `original_routing` and `original_endpoint`, assign
                        # the merged routing ONLY on the duplicated function to
                        # ensure method's immutability.
                        endpoint = functools.partial(method)
                        functools.update_wrapper(endpoint, method)
                        endpoint.routing = merged_routing
                        endpoint.original_endpoint = method
                        yield sub_url, endpoint
                else:
                    # duplicates the function (partial) with a copy of the
                    # original __dict__ (update_wrapper) to keep a reference
                    # to `original_routing` and `original_endpoint`, assign
                    # the merged routing ONLY on the duplicated function to
                    # ensure method's immutability.
                    endpoint = functools.partial(method)
                    functools.update_wrapper(endpoint, method)
                    endpoint.routing = merged_routing
                    endpoint.original_endpoint = method
                    yield url, endpoint


# Compatibility functions for existing code
def _generate_routing_rules(modules, nodb_only=False):
    """Wrapper for routing rule generation."""
    return generate_routing_rules(modules, nodb_only)


# Middleware for route processing
class RouteMiddleware:
    """Middleware to handle route processing."""
    
    def __init__(self, app):
        self.app = app

    async def __call__(self, scope, receive, send):
        """ASGI middleware interface."""
        if scope['type'] == 'http':
            path = scope['path']
            route_info = route_registry.get_route(path)
            
            if route_info:
                # Handle route
                try:
                    controller_class = route_info['controller']
                    method = route_info['method']
                    
                    # Create controller instance
                    controller = controller_class()
                    
                    # Call async method
                    result = await method(controller)
                    
                    # Convert result to ASGI response
                    if hasattr(result, '__call__'):
                        await result(scope, receive, send)
                    else:
                        # Simple string/bytes response
                        await send({
                            'type': 'http.response.start',
                            'status': 200,
                            'headers': [[b'content-type', b'text/html']],
                        })
                        await send({
                            'type': 'http.response.body',
                            'body': str(result).encode('utf-8'),
                        })
                except Exception as e:
                    _logger.error(f"Error in async route: {e}", exc_info=True)
                    await send({
                        'type': 'http.response.start',
                        'status': 500,
                        'headers': [],
                    })
                    await send({
                        'type': 'http.response.body',
                        'body': b'Internal Server Error',
                    })
            else:
                # Pass to next middleware/app
                await self.app(scope, receive, send)
        else:
            # Pass non-HTTP requests to next middleware/app
            await self.app(scope, receive, send)
