# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

import asyncio
import logging
from functools import partial

from odoo.api import registry_manager
from odoo.tools._vendor.sessions import FilesystemSessionStore

_logger = logging.getLogger(__name__)


class Session:
    """Odoo session with proper timing for authentication."""

    async def authenticate(self, dbname, credential):
        """
        Authenticate the current user with the given db, login and
        credential asynchronously. This ensures proper timing between
        database creation and authentication.
        """
        from . import request
        
        user_agent_env = {
            'interactive': True,
            'base_location': request.httprequest.url_root.rstrip('/'),
            'HTTP_HOST': request.httprequest.environ['HTTP_HOST'],
            'REMOTE_ADDR': request.httprequest.environ['REMOTE_ADDR'],
        }

        # Wait for registry and res.users model to be available
        try:
            await registry_manager.wait_for_model(dbname, 'res.users', timeout=30)
            registry = await registry_manager.get_registry(dbname)
            sync_registry = registry.get_sync_registry()
            
            if not sync_registry or 'res.users' not in sync_registry:
                raise Exception(f"res.users model not available in registry for database {dbname}")
            
        except Exception as e:
            _logger.error("Failed to get registry or res.users model: %s", e)
            raise

        # Perform authentication in thread pool since ORM is not fully async
        loop = asyncio.get_event_loop()
        auth_func = partial(self._sync_authenticate, sync_registry, dbname, credential, user_agent_env)
        auth_info = await loop.run_in_executor(None, auth_func)
        
        return auth_info

    def _sync_authenticate(self, registry, dbname, credential, user_agent_env):
        """Synchronous authentication helper."""
        import odoo.api
        
        auth_info = registry['res.users'].authenticate(dbname, credential, user_agent_env)
        pre_uid = auth_info['uid']

        self.uid = None
        self.pre_login = credential['login']
        self.pre_uid = pre_uid

        with registry.cursor() as cr:
            env = odoo.api.Environment(cr, pre_uid, {})

            # if 2FA is disabled we finalize immediately
            user = env['res.users'].browse(pre_uid)
            if auth_info.get('mfa') == 'skip' or not user._mfa_url():
                self.finalize(env)

        # Update request context if needed
        from . import request
        if request and request.session is self and request.db == dbname:
            request.env = odoo.api.Environment(request.env.cr, self.uid, self.context)
            from odoo.http.utils import get_lang
            request.update_context(lang=get_lang(request.env(user=pre_uid)).code)
            # request env needs to be able to access the latest changes from the auth layers
            request.env.cr.commit()

        return auth_info

    async def finalize_async(self, env):
        """
        Async version of finalize for partial sessions.
        """
        loop = asyncio.get_event_loop()
        finalize_func = partial(self.finalize, env)
        await loop.run_in_executor(None, finalize_func)


async def authenticate_session(session, dbname, credential):
    """
    Helper function to authenticate a session asynchronously.
    
    This function ensures proper timing and error handling for
    authentication in ASGI context.
    """
    if isinstance(session, AsyncSession):
        return await session.authenticate(dbname, credential)
    else:
        # Convert regular session to async session
        session_async = AsyncSession()
        session_async.__dict__.update(session.__dict__)
        return await session_async.authenticate(dbname, credential)


async def wait_for_authentication_ready(dbname, timeout=30):
    """
    Wait for a database to be ready for authentication.
    
    This ensures that:
    1. The database exists
    2. The registry is loaded
    3. The res.users model is available
    """
    try:
        # Wait for registry to be ready
        registry = await registry_manager.wait_for_registry(dbname, timeout)

        # Wait for res.users model specifically
        await registry_manager.wait_for_model(dbname, 'res.users', timeout)
        
        # Additional check to ensure the model is actually functional
        sync_registry = registry.get_sync_registry()
        if not sync_registry or 'res.users' not in sync_registry:
            raise Exception(f"res.users model not properly loaded for database {dbname}")
        
        _logger.info("Database %s is ready for authentication", dbname)
        return True
        
    except Exception as e:
        _logger.error("Database %s not ready for authentication: %s", dbname, e)
        raise


class SessionStore:
    """Session store for better session management."""
    
    def __init__(self, session_store):
        self.sync_store = session_store
        self._lock = asyncio.Lock()

    async def get(self, session_id):
        """Get session asynchronously."""
        async with self._lock:
            loop = asyncio.get_event_loop()
            get_func = partial(self.sync_store.get, session_id)
            session = await loop.run_in_executor(None, get_func)
            
            # Convert to async session if needed
            if session and not isinstance(session, AsyncSession):
                session_async = AsyncSession()
                session_async.__dict__.update(session.__dict__)
                return session_async
            
            return session

    async def new(self):
        """Create new session asynchronously."""
        async with self._lock:
            loop = asyncio.get_event_loop()
            new_func = partial(self.sync_store.new)
            session = await loop.run_in_executor(None, new_func)
            
            # Convert to async session
            session_async = AsyncSession()
            session_async.__dict__.update(session.__dict__)
            return session_async

    async def save(self, session):
        """Save session asynchronously."""
        async with self._lock:
            loop = asyncio.get_event_loop()
            save_func = partial(self.sync_store.save, session)
            await loop.run_in_executor(None, save_func)

    def is_valid_key(self, session_id):
        """Check if session key is valid (sync operation)."""
        return self.sync_store.is_valid_key(session_id)


# Helper function to patch existing session authentication
def patch_session_authentication():
    """Patch the session authentication to be async-aware."""
    original_authenticate = Session.authenticate
    
    def authenticate_with_support(self, dbname, credential):
        """Enhanced authenticate method with async support."""
        try:
            # Check if we're in an async context
            try:
                loop = asyncio.get_running_loop()
                # We're in an async context, but this is a sync call
                # This shouldn't happen in proper async code, but handle it gracefully
                _logger.warning("Sync authenticate called in async context. Consider using authenticate method.")
                return original_authenticate(self, dbname, credential)
            except RuntimeError:
                # No running loop, we're in sync context
                return original_authenticate(self, dbname, credential)
        except Exception as e:
            _logger.error("Authentication failed: %s", e)
            raise
    
    Session.authenticate = authenticate_with_support


# Apply the patch when module is loaded
patch_session_authentication()
