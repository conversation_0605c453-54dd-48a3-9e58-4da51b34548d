# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
Async error handling and retry mechanisms for Odoo.
This module provides robust error handling for async operations in ASGI context.
"""

import asyncio
import logging
import time
import traceback
from functools import wraps
from typing import Any, Callable, Optional, Type, Union, List
from contextlib import asynccontextmanager

_logger = logging.getLogger(__name__)


class RetryError(Exception):
    """Exception raised when all retry attempts are exhausted."""

    def __init__(self, message: str, last_exception: Exception, attempts: int):
        super().__init__(message)
        self.last_exception = last_exception
        self.attempts = attempts


class TimeoutError(Exception):
    """Exception raised when operation times out."""
    pass


class DatabaseError(Exception):
    """Exception raised for database-related errors."""
    pass


class RegistryError(Exception):
    """Exception raised for registry-related errors."""
    pass


class RetryConfig:
    """Configuration for retry mechanisms."""
    
    def __init__(self, 
                 max_attempts: int = 3,
                 base_delay: float = 1.0,
                 max_delay: float = 60.0,
                 exponential_base: float = 2.0,
                 jitter: bool = True,
                 retryable_exceptions: Optional[List[Type[Exception]]] = None):
        self.max_attempts = max_attempts
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.exponential_base = exponential_base
        self.jitter = jitter
        self.retryable_exceptions = retryable_exceptions or [
            ConnectionError,
            TimeoutError,
            DatabaseError,
            RegistryError,
        ]

    def calculate_delay(self, attempt: int) -> float:
        """Calculate delay for given attempt number."""
        delay = self.base_delay * (self.exponential_base ** (attempt - 1))
        delay = min(delay, self.max_delay)
        
        if self.jitter:
            import random
            delay *= (0.5 + random.random() * 0.5)  # Add 0-50% jitter
        
        return delay

    def is_retryable(self, exception: Exception) -> bool:
        """Check if exception is retryable."""
        return any(isinstance(exception, exc_type) for exc_type in self.retryable_exceptions)


def retry(config: Optional[RetryConfig] = None):
    """Decorator for retry functionality."""
    if config is None:
        config = RetryConfig()
    
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(1, config.max_attempts + 1):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    
                    if not config.is_retryable(e):
                        _logger.error("Non-retryable error in %s: %s", func.__name__, e)
                        raise
                    
                    if attempt == config.max_attempts:
                        _logger.error("All retry attempts exhausted for %s: %s", func.__name__, e)
                        raise RetryError(
                            f"Failed after {config.max_attempts} attempts",
                            e,
                            attempt
                        )
                    
                    delay = config.calculate_delay(attempt)
                    _logger.warning(
                        "Attempt %d/%d failed for %s: %s. Retrying in %.2f seconds",
                        attempt, config.max_attempts, func.__name__, e, delay
                    )
                    await asyncio.sleep(delay)
            
            # This should never be reached, but just in case
            raise RetryError("Unexpected retry loop exit", last_exception, config.max_attempts)
        
        return wrapper
    return decorator


async def timeout(coro, timeout_seconds: float):
    """Add timeout to operation."""
    try:
        return await asyncio.wait_for(coro, timeout=timeout_seconds)
    except asyncio.TimeoutError:
        raise TimeoutError(f"Operation timed out after {timeout_seconds} seconds")


@asynccontextmanager
async def error_context(operation_name: str,
                        reraise: bool = True,
                        log_level: int = logging.ERROR):
    """Context manager for error handling."""
    start_time = time.time()
    try:
        _logger.debug("Starting async operation: %s", operation_name)
        yield
        duration = time.time() - start_time
        _logger.debug("Completed async operation: %s (%.3f seconds)", operation_name, duration)
    except Exception as e:
        duration = time.time() - start_time
        _logger.log(
            log_level,
            "Failed async operation: %s (%.3f seconds): %s",
            operation_name, duration, e,
            exc_info=True
        )
        if reraise:
            raise


class CircuitBreaker:
    """Circuit breaker pattern for operations."""
    
    def __init__(self, 
                 failure_threshold: int = 5,
                 recovery_timeout: float = 60.0,
                 expected_exception: Type[Exception] = Exception):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        
        self.failure_count = 0
        self.last_failure_time = None
        self.state = 'CLOSED'  # CLOSED, OPEN, HALF_OPEN

    async def __aenter__(self):
        if self.state == 'OPEN':
            if time.time() - self.last_failure_time > self.recovery_timeout:
                self.state = 'HALF_OPEN'
                _logger.info("Circuit breaker transitioning to HALF_OPEN")
            else:
                raise RetryError("Circuit breaker is OPEN", None, 0)
        
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if exc_type is None:
            # Success
            if self.state == 'HALF_OPEN':
                self.state = 'CLOSED'
                self.failure_count = 0
                _logger.info("Circuit breaker reset to CLOSED")
        elif issubclass(exc_type, self.expected_exception):
            # Expected failure
            self.failure_count += 1
            self.last_failure_time = time.time()
            
            if self.failure_count >= self.failure_threshold:
                self.state = 'OPEN'
                _logger.warning("Circuit breaker opened due to %d failures", self.failure_count)


class Bulkhead:
    """Bulkhead pattern for operations to prevent resource exhaustion."""
    
    def __init__(self, max_concurrent: int = 10):
        self.semaphore = asyncio.Semaphore(max_concurrent)
        self.max_concurrent = max_concurrent

    async def __aenter__(self):
        await self.semaphore.acquire()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        self.semaphore.release()


# Specific error handlers for Odoo components
class DatabaseErrorHandler:
    """Error handler for database operations."""

    @staticmethod
    @retry(RetryConfig(
        max_attempts=3,
        base_delay=0.5,
        retryable_exceptions=[ConnectionError, DatabaseError]
    ))
    async def with_retry(coro):
        """Execute database operation with retry."""
        return await coro

    @staticmethod
    async def with_timeout(coro, timeout: float = 30.0):
        """Execute database operation with timeout."""
        return await timeout(coro, timeout)

    @staticmethod
    @asynccontextmanager
    async def error_context(operation_name: str):
        """Database-specific error context."""
        async with error_context(f"Database: {operation_name}"):
            yield


class RegistryErrorHandler:
    """Error handler for registry operations."""

    @staticmethod
    @retry(RetryConfig(
        max_attempts=5,
        base_delay=1.0,
        retryable_exceptions=[RegistryError, ConnectionError]
    ))
    async def with_retry(coro):
        """Execute registry operation with retry."""
        return await coro

    @staticmethod
    async def with_timeout(coro, timeout: float = 60.0):
        """Execute registry operation with timeout."""
        return await timeout(coro, timeout)

    @staticmethod
    @asynccontextmanager
    async def error_context(operation_name: str):
        """Registry-specific error context."""
        async with error_context(f"Registry: {operation_name}"):
            yield


# Global error handlers
async def handle_exception(exc: Exception, context: str = "Unknown"):
    """Global exception handler."""
    _logger.error("Unhandled async exception in %s: %s", context, exc, exc_info=True)
    
    # Send to error tracking service if configured
    try:
        # This would integrate with Sentry or similar
        pass
    except Exception as e:
        _logger.error("Failed to send error to tracking service: %s", e)


def setup_exception_handler():
    """Setup global exception handler."""
    def exception_handler(loop, context):
        exception = context.get('exception')
        if exception:
            asyncio.create_task(handle_exception(exception, str(context)))
        else:
            _logger.error("Error without exception: %s", context)
    
    try:
        loop = asyncio.get_running_loop()
        loop.set_exception_handler(exception_handler)
    except RuntimeError:
        _logger.warning("No running event loop to set exception handler")


# Utility functions for common error patterns
async def safe_call(coro, default_value=None, log_errors=True):
    """Safely call function with default value on error."""
    try:
        return await coro
    except Exception as e:
        if log_errors:
            _logger.error("Safe async call failed: %s", e, exc_info=True)
        return default_value


async def gather_with_errors(*coros, return_exceptions=True):
    """Gather operations with better error handling."""
    results = await asyncio.gather(*coros, return_exceptions=return_exceptions)
    
    errors = []
    successes = []
    
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            errors.append((i, result))
        else:
            successes.append((i, result))
    
    if errors:
        _logger.warning("Async gather completed with %d errors out of %d operations", 
                       len(errors), len(results))
        for i, error in errors:
            _logger.error("Operation %d failed: %s", i, error)
    
    return results, errors, successes


# Initialize error handling when module is loaded
setup_exception_handler()
