# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
Async HTTP utilities for Odoo.
This module provides async versions of HTTP operations to improve ASGI performance.
"""

import asyncio
import aiohttp
import logging
import json
from typing import Optional, Dict, Any, Union
from urllib.parse import urljoin

_logger = logging.getLogger(__name__)


class HTTPClient:
    """HTTP client for Odoo operations."""
    
    def __init__(self, timeout=30, max_connections=100):
        self.timeout = aiohttp.ClientTimeout(total=timeout)
        self.connector = aiohttp.TCPConnector(limit=max_connections)
        self._session = None

    async def __aenter__(self):
        self._session = aiohttp.ClientSession(
            timeout=self.timeout,
            connector=self.connector
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self._session:
            await self._session.close()

    async def get(self, url: str, headers: Optional[Dict] = None, params: Optional[Dict] = None) -> Dict[str, Any]:
        """Async GET request."""
        try:
            async with self._session.get(url, headers=headers, params=params) as response:
                response.raise_for_status()
                content_type = response.headers.get('content-type', '')
                
                if 'application/json' in content_type:
                    data = await response.json()
                else:
                    data = await response.text()
                
                return {
                    'status': response.status,
                    'headers': dict(response.headers),
                    'data': data
                }
        except Exception as e:
            _logger.error("Async GET request to %s failed: %s", url, e)
            raise

    async def post(self, url: str, data: Optional[Union[Dict, str]] = None, 
                   json_data: Optional[Dict] = None, headers: Optional[Dict] = None) -> Dict[str, Any]:
        """Async POST request."""
        try:
            kwargs = {'headers': headers}
            if json_data:
                kwargs['json'] = json_data
            elif data:
                kwargs['data'] = data
            
            async with self._session.post(url, **kwargs) as response:
                response.raise_for_status()
                content_type = response.headers.get('content-type', '')
                
                if 'application/json' in content_type:
                    response_data = await response.json()
                else:
                    response_data = await response.text()
                
                return {
                    'status': response.status,
                    'headers': dict(response.headers),
                    'data': response_data
                }
        except Exception as e:
            _logger.error("Async POST request to %s failed: %s", url, e)
            raise

    async def put(self, url: str, data: Optional[Union[Dict, str]] = None,
                  json_data: Optional[Dict] = None, headers: Optional[Dict] = None) -> Dict[str, Any]:
        """Async PUT request."""
        try:
            kwargs = {'headers': headers}
            if json_data:
                kwargs['json'] = json_data
            elif data:
                kwargs['data'] = data
            
            async with self._session.put(url, **kwargs) as response:
                response.raise_for_status()
                content_type = response.headers.get('content-type', '')
                
                if 'application/json' in content_type:
                    response_data = await response.json()
                else:
                    response_data = await response.text()
                
                return {
                    'status': response.status,
                    'headers': dict(response.headers),
                    'data': response_data
                }
        except Exception as e:
            _logger.error("Async PUT request to %s failed: %s", url, e)
            raise

    async def delete(self, url: str, headers: Optional[Dict] = None) -> Dict[str, Any]:
        """Async DELETE request."""
        try:
            async with self._session.delete(url, headers=headers) as response:
                response.raise_for_status()
                content_type = response.headers.get('content-type', '')
                
                if 'application/json' in content_type:
                    data = await response.json()
                else:
                    data = await response.text()
                
                return {
                    'status': response.status,
                    'headers': dict(response.headers),
                    'data': data
                }
        except Exception as e:
            _logger.error("Async DELETE request to %s failed: %s", url, e)
            raise

    async def download_file(self, url: str, file_path: str, headers: Optional[Dict] = None) -> bool:
        """Download file asynchronously."""
        try:
            from .async_file_utils import async_file_stream_write
            
            async with self._session.get(url, headers=headers) as response:
                response.raise_for_status()
                
                async def data_stream():
                    async for chunk in response.content.iter_chunked(8192):
                        yield chunk
                
                return await async_file_stream_write(file_path, data_stream())
        except Exception as e:
            _logger.error("Async file download from %s failed: %s", url, e)
            return False


# Global async HTTP client instance
_global_client = None


async def get_http_client():
    """Get or create global HTTP client."""
    global _global_client
    if _global_client is None:
        _global_client = HTTPClient()
        await _global_client.__aenter__()
    return _global_client


async def cleanup_http_client():
    """Cleanup global HTTP client."""
    global _global_client
    if _global_client:
        await _global_client.__aexit__(None, None, None)
        _global_client = None


# Convenience functions for common HTTP operations
async def get(url: str, headers: Optional[Dict] = None, params: Optional[Dict] = None) -> Dict[str, Any]:
    """Convenience function for GET request."""
    client = await get_http_client()
    return await client.get(url, headers, params)


async def post(url: str, data: Optional[Union[Dict, str]] = None,
               json_data: Optional[Dict] = None, headers: Optional[Dict] = None) -> Dict[str, Any]:
    """Convenience function for POST request."""
    client = await get_http_client()
    return await client.post(url, data, json_data, headers)


async def put(url: str, data: Optional[Union[Dict, str]] = None,
              json_data: Optional[Dict] = None, headers: Optional[Dict] = None) -> Dict[str, Any]:
    """Convenience function for PUT request."""
    client = await get_http_client()
    return await client.put(url, data, json_data, headers)


async def delete(url: str, headers: Optional[Dict] = None) -> Dict[str, Any]:
    """Convenience function for DELETE request."""
    client = await get_http_client()
    return await client.delete(url, headers)


async def download_file(url: str, file_path: str, headers: Optional[Dict] = None) -> bool:
    """Convenience function for file download."""
    client = await get_http_client()
    return await client.download_file(url, file_path, headers)


# Webhook handling
class WebhookHandler:
    """Handler for webhook processing."""

    def __init__(self, max_concurrent=10):
        self.semaphore = asyncio.Semaphore(max_concurrent)
        self.client = None

    async def __aenter__(self):
        self.client = HTTPClient()
        await self.client.__aenter__()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.client:
            await self.client.__aexit__(exc_type, exc_val, exc_tb)

    async def send_webhook(self, url: str, data: Dict, headers: Optional[Dict] = None) -> bool:
        """Send webhook asynchronously."""
        async with self.semaphore:
            try:
                response = await self.client.post(url, json_data=data, headers=headers)
                return 200 <= response['status'] < 300
            except Exception as e:
                _logger.error("Failed to send webhook to %s: %s", url, e)
                return False

    async def send_batch_webhooks(self, webhook_configs: list) -> list:
        """Send multiple webhooks concurrently."""
        tasks = []
        for config in webhook_configs:
            url = config['url']
            data = config['data']
            headers = config.get('headers')
            tasks.append(self.send_webhook(url, data, headers))
        
        return await asyncio.gather(*tasks, return_exceptions=True)


# API client for external services
class APIClient:
    """Generic API client for external services."""

    def __init__(self, base_url: str, api_key: Optional[str] = None, timeout: int = 30):
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.client = HTTPClient(timeout=timeout)
        self._session_started = False

    async def __aenter__(self):
        await self.client.__aenter__()
        self._session_started = True
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self._session_started:
            await self.client.__aexit__(exc_type, exc_val, exc_tb)

    def _get_headers(self, additional_headers: Optional[Dict] = None) -> Dict:
        """Get headers with API key if available."""
        headers = {'Content-Type': 'application/json'}
        if self.api_key:
            headers['Authorization'] = f'Bearer {self.api_key}'
        if additional_headers:
            headers.update(additional_headers)
        return headers

    def _build_url(self, endpoint: str) -> str:
        """Build full URL from endpoint."""
        return urljoin(self.base_url + '/', endpoint.lstrip('/'))

    async def get(self, endpoint: str, params: Optional[Dict] = None, headers: Optional[Dict] = None) -> Dict:
        """GET request to API endpoint."""
        url = self._build_url(endpoint)
        request_headers = self._get_headers(headers)
        return await self.client.get(url, request_headers, params)

    async def post(self, endpoint: str, data: Optional[Dict] = None, headers: Optional[Dict] = None) -> Dict:
        """POST request to API endpoint."""
        url = self._build_url(endpoint)
        request_headers = self._get_headers(headers)
        return await self.client.post(url, json_data=data, headers=request_headers)

    async def put(self, endpoint: str, data: Optional[Dict] = None, headers: Optional[Dict] = None) -> Dict:
        """PUT request to API endpoint."""
        url = self._build_url(endpoint)
        request_headers = self._get_headers(headers)
        return await self.client.put(url, json_data=data, headers=request_headers)

    async def delete(self, endpoint: str, headers: Optional[Dict] = None) -> Dict:
        """DELETE request to API endpoint."""
        url = self._build_url(endpoint)
        request_headers = self._get_headers(headers)
        return await self.client.delete(url, request_headers)
