# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
Async Compute Method Framework for Odoo ORM.
This module provides async versions of compute methods and field dependencies.
"""

import asyncio
import logging
import functools
from collections import defaultdict
from contextlib import asynccontextmanager
from typing import Any, Callable, Dict, List, Optional, Set, Union

from odoo.tools import frozendict
from .error_handling import retry, RetryConfig, timeout

_logger = logging.getLogger(__name__)


class ComputeError(Exception):
    """Exception raised during compute operations."""
    pass


class DependencyError(ComputeError):
    """Exception raised when dependencies cannot be resolved."""
    pass


class ComputeManager:
    """Manager for async compute operations and dependencies."""
    
    def __init__(self):
        self._compute_queue = asyncio.Queue()
        self._dependency_graph = defaultdict(set)
        self._reverse_dependencies = defaultdict(set)
        self._compute_locks = defaultdict(asyncio.Lock)
        self._running_computations = set()
        
    async def register_dependency(self, field_name: str, depends_on: List[str]):
        """Register field dependencies for async computation."""
        self._dependency_graph[field_name].update(depends_on)
        for dep in depends_on:
            self._reverse_dependencies[dep].add(field_name)
    
    async def get_computation_order(self, fields: List[str]) -> List[str]:
        """Get the correct order for computing fields based on dependencies."""
        visited = set()
        temp_visited = set()
        result = []
        
        def visit(field):
            if field in temp_visited:
                raise AsyncDependencyError(f"Circular dependency detected involving {field}")
            if field in visited:
                return
                
            temp_visited.add(field)
            for dep in self._dependency_graph.get(field, []):
                if dep in fields:
                    visit(dep)
            temp_visited.remove(field)
            visited.add(field)
            result.append(field)
        
        for field in fields:
            if field not in visited:
                visit(field)
        
        return result
    
    async def queue_computation(self, model, field_name: str, record_ids: List[int]):
        """Queue a field computation for async processing."""
        computation_id = f"{model._name}.{field_name}"
        if computation_id not in self._running_computations:
            await self._compute_queue.put((model, field_name, record_ids))
    
    async def process_compute_queue(self):
        """Process queued computations asynchronously."""
        while True:
            try:
                model, field_name, record_ids = await asyncio.wait_for(
                    self._compute_queue.get(), timeout=1.0
                )
                await self._execute_computation(model, field_name, record_ids)
                self._compute_queue.task_done()
            except asyncio.TimeoutError:
                # No more items in queue
                break
            except Exception as e:
                _logger.error("Error processing compute queue: %s", e)
    
    async def _execute_computation(self, model, field_name: str, record_ids: List[int]):
        """Execute a single field computation."""
        computation_id = f"{model._name}.{field_name}"
        
        async with self._compute_locks[computation_id]:
            if computation_id in self._running_computations:
                return
            
            self._running_computations.add(computation_id)
            try:
                field = model._fields[field_name]
                if hasattr(field, 'compute'):
                    records = model.browse(record_ids)
                    await field.compute(records)
                else:
                    # Fall back to sync computation
                    records = model.browse(record_ids)
                    field.compute(records)
            finally:
                self._running_computations.discard(computation_id)


# Global compute manager instance
_compute_manager = ComputeManager()


def depends(*args: str):
    """Decorator for computed field dependencies.

    Usage:
        @depends('partner_id.name', 'partner_id.is_company')
        async def _compute_pname(self):
            for record in self:
                if record.partner_id.is_company:
                    record.pname = (record.partner_id.name or "").upper()
                else:
                    record.pname = record.partner_id.name
    """
    def decorator(func: Callable) -> Callable:
        func._depends = args
        func._is_compute = True

        @functools.wraps(func)
        async def wrapper(self):
            return await func(self)

        return wrapper
    
    return decorator


def compute(func: Callable) -> Callable:
    """Decorator to mark a method as a compute method.

    Usage:
        @compute
        async def _compute_field_name(self):
            for record in self:
                # Async operations here
                data = await self._fetch_data_async()
                record.field_name = data
    """
    func._is_compute = True

    @functools.wraps(func)
    async def wrapper(self):
        return await func(self)

    return wrapper


@retry(RetryConfig(max_attempts=3, base_delay=0.1))
async def compute_with_retry(compute_func: Callable, records):
    """Execute compute function with retry logic."""
    return await compute_func(records)


@asynccontextmanager
async def compute_context(model, field_names: List[str]):
    """Context manager for compute operations."""
    try:
        _logger.debug("Starting compute for %s.%s", model._name, field_names)

        # Register dependencies
        for field_name in field_names:
            field = model._fields.get(field_name)
            if field and hasattr(field.compute, '_depends'):
                await _compute_manager.register_dependency(
                    field_name,
                    field.compute._depends
                )

        yield _compute_manager

    except Exception as e:
        _logger.error("Error in compute context: %s", e)
        raise
    finally:
        _logger.debug("Completed compute for %s.%s", model._name, field_names)


async def batch_compute(model, field_names: List[str], record_ids: List[int]):
    """Batch process multiple field computations asynchronously."""
    async with compute_context(model, field_names) as manager:
        # Get computation order based on dependencies
        ordered_fields = await manager.get_computation_order(field_names)
        
        # Group fields that can be computed concurrently
        concurrent_groups = []
        current_group = []
        
        for field_name in ordered_fields:
            field = model._fields[field_name]
            if hasattr(field.compute, '_depends'):
                # Check if any dependencies are in current group
                deps = field.compute._depends
                if any(dep.split('.')[0] in [f for f in current_group] for dep in deps):
                    # Start new group
                    if current_group:
                        concurrent_groups.append(current_group)
                    current_group = [field_name]
                else:
                    current_group.append(field_name)
            else:
                current_group.append(field_name)
        
        if current_group:
            concurrent_groups.append(current_group)
        
        # Execute groups sequentially, fields within groups concurrently
        for group in concurrent_groups:
            tasks = []
            for field_name in group:
                task = manager.queue_computation(model, field_name, record_ids)
                tasks.append(task)
            
            if tasks:
                await asyncio.gather(*tasks)
                await manager.process_compute_queue()


class ComputeField:
    """Mixin for fields that support computation."""

    def __init__(self, *args, compute=None, **kwargs):
        super().__init__(*args, **kwargs)
        if compute:
            self.compute = compute
            self._supports_compute = True
        else:
            self._supports_compute = False

    async def compute_value(self, records):
        """Compute value for this field."""
        if hasattr(self, 'compute') and self._supports_compute:
            return await self.compute(records)
        else:
            # Fall back to sync computation in executor
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(None, self.compute, records)


# Utility functions for async field operations

async def field_read(model, record_ids: List[int], field_names: List[str]):
    """Field reading with compute support."""
    records = model.browse(record_ids)

    # Identify fields that need computation
    compute_fields = []
    for field_name in field_names:
        field = model._fields.get(field_name)
        if field and field.compute:
            compute_fields.append(field_name)

    # Batch compute fields
    if compute_fields:
        await batch_compute(model, compute_fields, record_ids)
    
    # Read field values
    result = []
    for record in records:
        record_data = {}
        for field_name in field_names:
            record_data[field_name] = record[field_name]
        result.append(record_data)
    
    return result


async def invalidate_dependencies(model, field_names: List[str]):
    """Dependency invalidation."""
    tasks = []
    
    for field_name in field_names:
        # Find fields that depend on this field
        dependent_fields = _compute_manager._reverse_dependencies.get(field_name, set())
        
        for dep_field in dependent_fields:
            # Queue recomputation of dependent fields
            task = _compute_manager.queue_computation(
                model, dep_field, model._ids
            )
            tasks.append(task)
    
    if tasks:
        await asyncio.gather(*tasks)
        await _compute_manager.process_compute_queue()
