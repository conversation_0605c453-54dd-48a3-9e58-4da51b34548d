# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
Modular Odoo Models Package

This package contains the modularized Odoo ORM system, split into logical components
for better maintainability and async-ready architecture.
"""

# Import all components to maintain backward compatibility
from .model_constants import *
from .model_utils import *
from .model_exceptions import *
from .model_metaclass import MetaModel
from .base_model import BaseModel, Json
from .model_types import Model, TransientModel, AbstractModel

# Maintain backward compatibility by exposing all classes at package level
__all__ = [
    'BaseModel',
    'Model', 
    'TransientModel',
    'AbstractModel',
    'MetaModel',
]
