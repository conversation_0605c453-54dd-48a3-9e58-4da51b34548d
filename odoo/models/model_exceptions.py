# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

"""
Model Exceptions Module

This module contains error handling functions and PostgreSQL error conversion
utilities for the Odoo ORM models system. All functions are async-ready.
"""

from collections import defaultdict
from contextlib import closing
from .. import tools
from ..tools import SQL, format_list


def convert_pgerror_not_null(model, fields, info, e):
    """Convert PostgreSQL NOT NULL constraint error to user-friendly message."""
    env = model.env
    if e.diag.table_name != model._table:
        return {'message': env._("Missing required value for the field '%(name)s' on a linked model [%(linked_model)s]", name=e.diag.column_name, linked_model=e.diag.table_name)}

    field_name = e.diag.column_name
    field = fields[field_name]
    message = env._("Missing required value for the field '%(name)s' (%(technical_name)s)", name=field['string'], technical_name=field_name)
    return {
        'message': message,
        'field': field_name,
    }


async def convert_pgerror_unique(model, fields, info, e):
    """Convert PostgreSQL UNIQUE constraint error to user-friendly message."""
    # new cursor since we're probably in an error handler in a blown
    # transaction which may not have been rollbacked/cleaned yet
    async with model.env.registry.cursor() as cr_tmp:
        await cr_tmp.execute(SQL("""
            SELECT
                conname AS "constraint name",
                t.relname AS "table name",
                ARRAY(
                    SELECT attname FROM pg_attribute
                    WHERE attrelid = conrelid
                      AND attnum = ANY(conkey)
                ) as "columns"
            FROM pg_constraint
            JOIN pg_class t ON t.oid = conrelid
            WHERE conname = %s
        """, e.diag.constraint_name))
        constraint, table, ufields = await cr_tmp.fetchone() or (None, None, None)
    
    # if the unique constraint is on an expression or on an other table
    if not ufields or model._table != table:
        return {'message': tools.exception_to_unicode(e)}

    # TODO: add stuff from e.diag.message_hint? provides details about the constraint & duplication values but may be localized...
    if len(ufields) == 1:
        field_name = ufields[0]
        field = fields[field_name]
        message = model.env._(
            "The value for the field '%(field)s' already exists (this is probably '%(other_field)s' in the current model).",
            field=field_name,
            other_field=field['string'],
        )
        return {
            'message': message,
            'field': field_name,
        }
    
    field_strings = [fields[fname]['string'] for fname in ufields]
    message = model.env._(
        "The values for the fields '%(fields)s' already exist (they are probably '%(other_fields)s' in the current model).",
        fields=format_list(model.env, ufields),
        other_fields=format_list(model.env, field_strings),
    )
    return {
        'message': message,
        # no field, unclear which one we should pick and they could be in any order
    }


def convert_pgerror_constraint(model, fields, info, e):
    """Convert PostgreSQL CHECK constraint error to user-friendly message."""
    sql_constraints = dict([(('%s_%s') % (e.diag.table_name, x[0]), x) for x in model._sql_constraints])
    if e.diag.constraint_name in sql_constraints.keys():
        return {'message': "'%s'" % sql_constraints[e.diag.constraint_name][2]}
    return {'message': tools.exception_to_unicode(e)}


# PostgreSQL error code to conversion function mapping
PGERROR_TO_OE = defaultdict(
    # shape of mapped converters
    lambda: (lambda model, fvg, info, pgerror: {'message': tools.exception_to_unicode(pgerror)}),
    {
        '23502': convert_pgerror_not_null,  # NOT NULL constraint violation
        '23505': convert_pgerror_unique,    # UNIQUE constraint violation (async)
        '23514': convert_pgerror_constraint, # CHECK constraint violation
    },
)
