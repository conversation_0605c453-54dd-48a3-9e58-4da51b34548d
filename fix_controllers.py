#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to fix all non-async controller methods in addons/web/controllers/
"""

import os
import re
import glob

def fix_controller_file(filepath):
    """Fix a single controller file by making route methods async"""
    print(f"Processing {filepath}")
    
    with open(filepath, 'r') as f:
        content = f.read()
    
    # Pattern to match @http.route or @route followed by def (not async def)
    pattern = r'(@(?:http\.)?route\([^)]*\)[^\n]*\n\s*)def\s+(\w+)'
    
    def replace_func(match):
        route_decorator = match.group(1)
        method_name = match.group(2)
        return f"{route_decorator}async def {method_name}"
    
    new_content = re.sub(pattern, replace_func, content)
    
    if new_content != content:
        with open(filepath, 'w') as f:
            f.write(new_content)
        print(f"  Fixed {filepath}")
        return True
    else:
        print(f"  No changes needed for {filepath}")
        return False

def main():
    """Main function to fix all controller files"""
    controller_dir = "addons/web/controllers"
    
    if not os.path.exists(controller_dir):
        print(f"Directory {controller_dir} not found")
        return
    
    # Find all Python files in controllers directory
    pattern = os.path.join(controller_dir, "*.py")
    files = glob.glob(pattern)
    
    fixed_count = 0
    for filepath in files:
        if fix_controller_file(filepath):
            fixed_count += 1
    
    print(f"\nFixed {fixed_count} files")

if __name__ == "__main__":
    main()
